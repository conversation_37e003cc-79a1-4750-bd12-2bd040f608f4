/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n/* harmony import */ var _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CustomerDebt */ \"(rsc)/./src/lib/models/CustomerDebt.ts\");\n\n\n\n\n// GET /api/dashboard/stats - Get dashboard statistics\nasync function GET() {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get product statistics\n        const productStats = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: null,\n                    totalProducts: {\n                        $sum: 1\n                    },\n                    lowStockProducts: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $lte: [\n                                        '$stockQuantity',\n                                        5\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    totalStockValue: {\n                        $sum: {\n                            $multiply: [\n                                '$price',\n                                '$stockQuantity'\n                            ]\n                        }\n                    }\n                }\n            }\n        ]);\n        // Get debt statistics\n        const debtStats = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: null,\n                    totalCustomers: {\n                        $addToSet: '$customerName'\n                    },\n                    totalDebts: {\n                        $sum: 1\n                    },\n                    totalUnpaidDebts: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    totalDebtAmount: {\n                        $sum: '$totalAmount'\n                    },\n                    totalUnpaidAmount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                '$totalAmount',\n                                0\n                            ]\n                        }\n                    }\n                }\n            },\n            {\n                $project: {\n                    totalCustomers: {\n                        $size: '$totalCustomers'\n                    },\n                    totalDebts: 1,\n                    totalUnpaidDebts: 1,\n                    totalDebtAmount: 1,\n                    totalUnpaidAmount: 1,\n                    _id: 0\n                }\n            }\n        ]);\n        // Get recent debts\n        const recentDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({}, 'customerName productName totalAmount dateOfDebt isPaid').sort({\n            dateOfDebt: -1\n        }).limit(5).lean();\n        // Get low stock products\n        const lowStockProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            stockQuantity: {\n                $lte: 5\n            }\n        }, 'name stockQuantity price category').sort({\n            stockQuantity: 1\n        }).limit(5).lean();\n        // Get top customers by debt amount\n        const topCustomers = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].aggregate([\n            {\n                $match: {\n                    isPaid: false\n                }\n            },\n            {\n                $group: {\n                    _id: '$customerName',\n                    totalUnpaid: {\n                        $sum: '$totalAmount'\n                    },\n                    debtCount: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $project: {\n                    customerName: '$_id',\n                    totalUnpaid: 1,\n                    debtCount: 1,\n                    _id: 0\n                }\n            },\n            {\n                $sort: {\n                    totalUnpaid: -1\n                }\n            },\n            {\n                $limit: 5\n            }\n        ]);\n        const stats = {\n            products: productStats[0] || {\n                totalProducts: 0,\n                lowStockProducts: 0,\n                totalStockValue: 0\n            },\n            debts: debtStats[0] || {\n                totalCustomers: 0,\n                totalDebts: 0,\n                totalUnpaidDebts: 0,\n                totalDebtAmount: 0,\n                totalUnpaidAmount: 0\n            },\n            recentDebts,\n            lowStockProducts,\n            topCustomers\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats\n        });\n    } catch (error) {\n        console.error('Error fetching dashboard stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch dashboard statistics'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/CustomerDebt.ts":
/*!****************************************!*\
  !*** ./src/lib/models/CustomerDebt.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomerDebtSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    customerName: {\n        type: String,\n        required: [\n            true,\n            'Customer name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Customer name cannot exceed 100 characters'\n        ]\n    },\n    productName: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    productPrice: {\n        type: Number,\n        required: [\n            true,\n            'Product price is required'\n        ],\n        min: [\n            0,\n            'Product price cannot be negative'\n        ]\n    },\n    quantity: {\n        type: Number,\n        required: [\n            true,\n            'Quantity is required'\n        ],\n        min: [\n            1,\n            'Quantity must be at least 1'\n        ],\n        validate: {\n            validator: function(value) {\n                return Number.isInteger(value) && value > 0;\n            },\n            message: 'Quantity must be a positive integer'\n        }\n    },\n    totalAmount: {\n        type: Number,\n        required: [\n            true,\n            'Total amount is required'\n        ],\n        min: [\n            0,\n            'Total amount cannot be negative'\n        ]\n    },\n    dateOfDebt: {\n        type: Date,\n        required: [\n            true,\n            'Date of debt is required'\n        ],\n        default: Date.now\n    },\n    isPaid: {\n        type: Boolean,\n        default: false\n    },\n    paidDate: {\n        type: Date,\n        default: null\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Notes cannot exceed 500 characters'\n        ],\n        default: ''\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nCustomerDebtSchema.index({\n    customerName: 1\n});\nCustomerDebtSchema.index({\n    isPaid: 1\n});\nCustomerDebtSchema.index({\n    dateOfDebt: -1\n});\nCustomerDebtSchema.index({\n    customerName: 1,\n    isPaid: 1\n});\n// Virtual for days since debt was created\nCustomerDebtSchema.virtual('daysSinceDebt').get(function() {\n    const now = new Date();\n    const debtDate = new Date(this.dateOfDebt);\n    const diffTime = Math.abs(now.getTime() - debtDate.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware to calculate total amount and handle paid date\nCustomerDebtSchema.pre('save', function(next) {\n    // Calculate total amount\n    this.totalAmount = this.productPrice * this.quantity;\n    // Set paid date when marking as paid\n    if (this.isPaid && !this.paidDate) {\n        this.paidDate = new Date();\n    }\n    // Clear paid date when marking as unpaid\n    if (!this.isPaid && this.paidDate) {\n        this.paidDate = undefined;\n    }\n    next();\n});\n// Static method to get debt summary by customer\nCustomerDebtSchema.statics.getDebtSummaryByCustomer = async function(customerName) {\n    const debts = await this.find({\n        customerName\n    }).sort({\n        dateOfDebt: -1\n    });\n    const totalDebt = debts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    const totalUnpaid = debts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    return {\n        customerName,\n        totalDebt,\n        totalUnpaid,\n        debtCount: debts.length,\n        unpaidCount: debts.filter((debt)=>!debt.isPaid).length,\n        debts\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CustomerDebt || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('CustomerDebt', CustomerDebtSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vZGVscy9DdXN0b21lckRlYnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBT3RELE1BQU1FLHFCQUFxQixJQUFJRCw0Q0FBTUEsQ0FDbkM7SUFDRUUsY0FBYztRQUNaQyxNQUFNQztRQUNOQyxVQUFVO1lBQUM7WUFBTTtTQUE0QjtRQUM3Q0MsTUFBTTtRQUNOQyxXQUFXO1lBQUM7WUFBSztTQUE2QztJQUNoRTtJQUNBQyxhQUFhO1FBQ1hMLE1BQU1DO1FBQ05DLFVBQVU7WUFBQztZQUFNO1NBQTJCO1FBQzVDQyxNQUFNO1FBQ05DLFdBQVc7WUFBQztZQUFLO1NBQTRDO0lBQy9EO0lBQ0FFLGNBQWM7UUFDWk4sTUFBTU87UUFDTkwsVUFBVTtZQUFDO1lBQU07U0FBNEI7UUFDN0NNLEtBQUs7WUFBQztZQUFHO1NBQW1DO0lBQzlDO0lBQ0FDLFVBQVU7UUFDUlQsTUFBTU87UUFDTkwsVUFBVTtZQUFDO1lBQU07U0FBdUI7UUFDeENNLEtBQUs7WUFBQztZQUFHO1NBQThCO1FBQ3ZDRSxVQUFVO1lBQ1JDLFdBQVcsU0FBVUMsS0FBYTtnQkFDaEMsT0FBT0wsT0FBT00sU0FBUyxDQUFDRCxVQUFVQSxRQUFRO1lBQzVDO1lBQ0FFLFNBQVM7UUFDWDtJQUNGO0lBQ0FDLGFBQWE7UUFDWGYsTUFBTU87UUFDTkwsVUFBVTtZQUFDO1lBQU07U0FBMkI7UUFDNUNNLEtBQUs7WUFBQztZQUFHO1NBQWtDO0lBQzdDO0lBQ0FRLFlBQVk7UUFDVmhCLE1BQU1pQjtRQUNOZixVQUFVO1lBQUM7WUFBTTtTQUEyQjtRQUM1Q2dCLFNBQVNELEtBQUtFLEdBQUc7SUFDbkI7SUFDQUMsUUFBUTtRQUNOcEIsTUFBTXFCO1FBQ05ILFNBQVM7SUFDWDtJQUNBSSxVQUFVO1FBQ1J0QixNQUFNaUI7UUFDTkMsU0FBUztJQUNYO0lBQ0FLLE9BQU87UUFDTHZCLE1BQU1DO1FBQ05FLE1BQU07UUFDTkMsV0FBVztZQUFDO1lBQUs7U0FBcUM7UUFDdERjLFNBQVM7SUFDWDtBQUNGLEdBQ0E7SUFDRU0sWUFBWTtJQUNaQyxRQUFRO1FBQUVDLFVBQVU7SUFBSztJQUN6QkMsVUFBVTtRQUFFRCxVQUFVO0lBQUs7QUFDN0I7QUFHRix1Q0FBdUM7QUFDdkM1QixtQkFBbUI4QixLQUFLLENBQUM7SUFBRTdCLGNBQWM7QUFBRTtBQUMzQ0QsbUJBQW1COEIsS0FBSyxDQUFDO0lBQUVSLFFBQVE7QUFBRTtBQUNyQ3RCLG1CQUFtQjhCLEtBQUssQ0FBQztJQUFFWixZQUFZLENBQUM7QUFBRTtBQUMxQ2xCLG1CQUFtQjhCLEtBQUssQ0FBQztJQUFFN0IsY0FBYztJQUFHcUIsUUFBUTtBQUFFO0FBRXRELDBDQUEwQztBQUMxQ3RCLG1CQUFtQitCLE9BQU8sQ0FBQyxpQkFBaUJDLEdBQUcsQ0FBQztJQUM5QyxNQUFNWCxNQUFNLElBQUlGO0lBQ2hCLE1BQU1jLFdBQVcsSUFBSWQsS0FBSyxJQUFJLENBQUNELFVBQVU7SUFDekMsTUFBTWdCLFdBQVdDLEtBQUtDLEdBQUcsQ0FBQ2YsSUFBSWdCLE9BQU8sS0FBS0osU0FBU0ksT0FBTztJQUMxRCxPQUFPRixLQUFLRyxJQUFJLENBQUNKLFdBQVksUUFBTyxLQUFLLEtBQUssRUFBQztBQUNqRDtBQUVBLHFFQUFxRTtBQUNyRWxDLG1CQUFtQnVDLEdBQUcsQ0FBQyxRQUFRLFNBQVVDLElBQUk7SUFDM0MseUJBQXlCO0lBQ3pCLElBQUksQ0FBQ3ZCLFdBQVcsR0FBRyxJQUFJLENBQUNULFlBQVksR0FBRyxJQUFJLENBQUNHLFFBQVE7SUFFcEQscUNBQXFDO0lBQ3JDLElBQUksSUFBSSxDQUFDVyxNQUFNLElBQUksQ0FBQyxJQUFJLENBQUNFLFFBQVEsRUFBRTtRQUNqQyxJQUFJLENBQUNBLFFBQVEsR0FBRyxJQUFJTDtJQUN0QjtJQUVBLHlDQUF5QztJQUN6QyxJQUFJLENBQUMsSUFBSSxDQUFDRyxNQUFNLElBQUksSUFBSSxDQUFDRSxRQUFRLEVBQUU7UUFDakMsSUFBSSxDQUFDQSxRQUFRLEdBQUdpQjtJQUNsQjtJQUVBRDtBQUNGO0FBRUEsZ0RBQWdEO0FBQ2hEeEMsbUJBQW1CMEMsT0FBTyxDQUFDQyx3QkFBd0IsR0FBRyxlQUNwRDFDLFlBQW9CO0lBRXBCLE1BQU0yQyxRQUFRLE1BQU0sSUFBSSxDQUFDQyxJQUFJLENBQUM7UUFBRTVDO0lBQWEsR0FBRzZDLElBQUksQ0FBQztRQUFFNUIsWUFBWSxDQUFDO0lBQUU7SUFFdEUsTUFBTTZCLFlBQVlILE1BQU1JLE1BQU0sQ0FDNUIsQ0FBQ0MsS0FBYUMsT0FBY0QsTUFBTUMsS0FBS2pDLFdBQVcsRUFDbEQ7SUFFRixNQUFNa0MsY0FBY1AsTUFDakJRLE1BQU0sQ0FBQyxDQUFDRixPQUFjLENBQUNBLEtBQUs1QixNQUFNLEVBQ2xDMEIsTUFBTSxDQUFDLENBQUNDLEtBQWFDLE9BQWNELE1BQU1DLEtBQUtqQyxXQUFXLEVBQUU7SUFFOUQsT0FBTztRQUNMaEI7UUFDQThDO1FBQ0FJO1FBQ0FFLFdBQVdULE1BQU1VLE1BQU07UUFDdkJDLGFBQWFYLE1BQU1RLE1BQU0sQ0FBQyxDQUFDRixPQUFjLENBQUNBLEtBQUs1QixNQUFNLEVBQUVnQyxNQUFNO1FBQzdEVjtJQUNGO0FBQ0Y7QUFFQSxpRUFBZTlDLHdEQUFlLENBQUMyRCxZQUFZLElBQ3pDM0QscURBQWMsQ0FBdUIsZ0JBQWdCRSxtQkFBbUJBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcREVMTFxcT25lRHJpdmVcXERlc2t0b3BcXHRpbmRhaGFuXFxhcHBcXHNyY1xcbGliXFxtb2RlbHNcXEN1c3RvbWVyRGVidC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UsIHsgU2NoZW1hLCBEb2N1bWVudCB9IGZyb20gJ21vbmdvb3NlJztcbmltcG9ydCB7IEN1c3RvbWVyRGVidCBhcyBJQ3VzdG9tZXJEZWJ0IH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEN1c3RvbWVyRGVidERvY3VtZW50XG4gIGV4dGVuZHMgT21pdDxJQ3VzdG9tZXJEZWJ0LCAnX2lkJz4sXG4gICAgRG9jdW1lbnQge31cblxuY29uc3QgQ3VzdG9tZXJEZWJ0U2NoZW1hID0gbmV3IFNjaGVtYTxDdXN0b21lckRlYnREb2N1bWVudD4oXG4gIHtcbiAgICBjdXN0b21lck5hbWU6IHtcbiAgICAgIHR5cGU6IFN0cmluZyxcbiAgICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ0N1c3RvbWVyIG5hbWUgaXMgcmVxdWlyZWQnXSxcbiAgICAgIHRyaW06IHRydWUsXG4gICAgICBtYXhsZW5ndGg6IFsxMDAsICdDdXN0b21lciBuYW1lIGNhbm5vdCBleGNlZWQgMTAwIGNoYXJhY3RlcnMnXSxcbiAgICB9LFxuICAgIHByb2R1Y3ROYW1lOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdQcm9kdWN0IG5hbWUgaXMgcmVxdWlyZWQnXSxcbiAgICAgIHRyaW06IHRydWUsXG4gICAgICBtYXhsZW5ndGg6IFsxMDAsICdQcm9kdWN0IG5hbWUgY2Fubm90IGV4Y2VlZCAxMDAgY2hhcmFjdGVycyddLFxuICAgIH0sXG4gICAgcHJvZHVjdFByaWNlOiB7XG4gICAgICB0eXBlOiBOdW1iZXIsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdQcm9kdWN0IHByaWNlIGlzIHJlcXVpcmVkJ10sXG4gICAgICBtaW46IFswLCAnUHJvZHVjdCBwcmljZSBjYW5ub3QgYmUgbmVnYXRpdmUnXSxcbiAgICB9LFxuICAgIHF1YW50aXR5OiB7XG4gICAgICB0eXBlOiBOdW1iZXIsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdRdWFudGl0eSBpcyByZXF1aXJlZCddLFxuICAgICAgbWluOiBbMSwgJ1F1YW50aXR5IG11c3QgYmUgYXQgbGVhc3QgMSddLFxuICAgICAgdmFsaWRhdGU6IHtcbiAgICAgICAgdmFsaWRhdG9yOiBmdW5jdGlvbiAodmFsdWU6IG51bWJlcikge1xuICAgICAgICAgIHJldHVybiBOdW1iZXIuaXNJbnRlZ2VyKHZhbHVlKSAmJiB2YWx1ZSA+IDA7XG4gICAgICAgIH0sXG4gICAgICAgIG1lc3NhZ2U6ICdRdWFudGl0eSBtdXN0IGJlIGEgcG9zaXRpdmUgaW50ZWdlcicsXG4gICAgICB9LFxuICAgIH0sXG4gICAgdG90YWxBbW91bnQ6IHtcbiAgICAgIHR5cGU6IE51bWJlcixcbiAgICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ1RvdGFsIGFtb3VudCBpcyByZXF1aXJlZCddLFxuICAgICAgbWluOiBbMCwgJ1RvdGFsIGFtb3VudCBjYW5ub3QgYmUgbmVnYXRpdmUnXSxcbiAgICB9LFxuICAgIGRhdGVPZkRlYnQ6IHtcbiAgICAgIHR5cGU6IERhdGUsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdEYXRlIG9mIGRlYnQgaXMgcmVxdWlyZWQnXSxcbiAgICAgIGRlZmF1bHQ6IERhdGUubm93LFxuICAgIH0sXG4gICAgaXNQYWlkOiB7XG4gICAgICB0eXBlOiBCb29sZWFuLFxuICAgICAgZGVmYXVsdDogZmFsc2UsXG4gICAgfSxcbiAgICBwYWlkRGF0ZToge1xuICAgICAgdHlwZTogRGF0ZSxcbiAgICAgIGRlZmF1bHQ6IG51bGwsXG4gICAgfSxcbiAgICBub3Rlczoge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgdHJpbTogdHJ1ZSxcbiAgICAgIG1heGxlbmd0aDogWzUwMCwgJ05vdGVzIGNhbm5vdCBleGNlZWQgNTAwIGNoYXJhY3RlcnMnXSxcbiAgICAgIGRlZmF1bHQ6ICcnLFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICB0aW1lc3RhbXBzOiB0cnVlLFxuICAgIHRvSlNPTjogeyB2aXJ0dWFsczogdHJ1ZSB9LFxuICAgIHRvT2JqZWN0OiB7IHZpcnR1YWxzOiB0cnVlIH0sXG4gIH1cbik7XG5cbi8vIEluZGV4ZXMgZm9yIGJldHRlciBxdWVyeSBwZXJmb3JtYW5jZVxuQ3VzdG9tZXJEZWJ0U2NoZW1hLmluZGV4KHsgY3VzdG9tZXJOYW1lOiAxIH0pO1xuQ3VzdG9tZXJEZWJ0U2NoZW1hLmluZGV4KHsgaXNQYWlkOiAxIH0pO1xuQ3VzdG9tZXJEZWJ0U2NoZW1hLmluZGV4KHsgZGF0ZU9mRGVidDogLTEgfSk7XG5DdXN0b21lckRlYnRTY2hlbWEuaW5kZXgoeyBjdXN0b21lck5hbWU6IDEsIGlzUGFpZDogMSB9KTtcblxuLy8gVmlydHVhbCBmb3IgZGF5cyBzaW5jZSBkZWJ0IHdhcyBjcmVhdGVkXG5DdXN0b21lckRlYnRTY2hlbWEudmlydHVhbCgnZGF5c1NpbmNlRGVidCcpLmdldChmdW5jdGlvbiAoKSB7XG4gIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gIGNvbnN0IGRlYnREYXRlID0gbmV3IERhdGUodGhpcy5kYXRlT2ZEZWJ0KTtcbiAgY29uc3QgZGlmZlRpbWUgPSBNYXRoLmFicyhub3cuZ2V0VGltZSgpIC0gZGVidERhdGUuZ2V0VGltZSgpKTtcbiAgcmV0dXJuIE1hdGguY2VpbChkaWZmVGltZSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7XG59KTtcblxuLy8gUHJlLXNhdmUgbWlkZGxld2FyZSB0byBjYWxjdWxhdGUgdG90YWwgYW1vdW50IGFuZCBoYW5kbGUgcGFpZCBkYXRlXG5DdXN0b21lckRlYnRTY2hlbWEucHJlKCdzYXZlJywgZnVuY3Rpb24gKG5leHQpIHtcbiAgLy8gQ2FsY3VsYXRlIHRvdGFsIGFtb3VudFxuICB0aGlzLnRvdGFsQW1vdW50ID0gdGhpcy5wcm9kdWN0UHJpY2UgKiB0aGlzLnF1YW50aXR5O1xuXG4gIC8vIFNldCBwYWlkIGRhdGUgd2hlbiBtYXJraW5nIGFzIHBhaWRcbiAgaWYgKHRoaXMuaXNQYWlkICYmICF0aGlzLnBhaWREYXRlKSB7XG4gICAgdGhpcy5wYWlkRGF0ZSA9IG5ldyBEYXRlKCk7XG4gIH1cblxuICAvLyBDbGVhciBwYWlkIGRhdGUgd2hlbiBtYXJraW5nIGFzIHVucGFpZFxuICBpZiAoIXRoaXMuaXNQYWlkICYmIHRoaXMucGFpZERhdGUpIHtcbiAgICB0aGlzLnBhaWREYXRlID0gdW5kZWZpbmVkO1xuICB9XG5cbiAgbmV4dCgpO1xufSk7XG5cbi8vIFN0YXRpYyBtZXRob2QgdG8gZ2V0IGRlYnQgc3VtbWFyeSBieSBjdXN0b21lclxuQ3VzdG9tZXJEZWJ0U2NoZW1hLnN0YXRpY3MuZ2V0RGVidFN1bW1hcnlCeUN1c3RvbWVyID0gYXN5bmMgZnVuY3Rpb24gKFxuICBjdXN0b21lck5hbWU6IHN0cmluZ1xuKSB7XG4gIGNvbnN0IGRlYnRzID0gYXdhaXQgdGhpcy5maW5kKHsgY3VzdG9tZXJOYW1lIH0pLnNvcnQoeyBkYXRlT2ZEZWJ0OiAtMSB9KTtcblxuICBjb25zdCB0b3RhbERlYnQgPSBkZWJ0cy5yZWR1Y2UoXG4gICAgKHN1bTogbnVtYmVyLCBkZWJ0OiBhbnkpID0+IHN1bSArIGRlYnQudG90YWxBbW91bnQsXG4gICAgMFxuICApO1xuICBjb25zdCB0b3RhbFVucGFpZCA9IGRlYnRzXG4gICAgLmZpbHRlcigoZGVidDogYW55KSA9PiAhZGVidC5pc1BhaWQpXG4gICAgLnJlZHVjZSgoc3VtOiBudW1iZXIsIGRlYnQ6IGFueSkgPT4gc3VtICsgZGVidC50b3RhbEFtb3VudCwgMCk7XG5cbiAgcmV0dXJuIHtcbiAgICBjdXN0b21lck5hbWUsXG4gICAgdG90YWxEZWJ0LFxuICAgIHRvdGFsVW5wYWlkLFxuICAgIGRlYnRDb3VudDogZGVidHMubGVuZ3RoLFxuICAgIHVucGFpZENvdW50OiBkZWJ0cy5maWx0ZXIoKGRlYnQ6IGFueSkgPT4gIWRlYnQuaXNQYWlkKS5sZW5ndGgsXG4gICAgZGVidHMsXG4gIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCBtb25nb29zZS5tb2RlbHMuQ3VzdG9tZXJEZWJ0IHx8XG4gIG1vbmdvb3NlLm1vZGVsPEN1c3RvbWVyRGVidERvY3VtZW50PignQ3VzdG9tZXJEZWJ0JywgQ3VzdG9tZXJEZWJ0U2NoZW1hKTtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIlNjaGVtYSIsIkN1c3RvbWVyRGVidFNjaGVtYSIsImN1c3RvbWVyTmFtZSIsInR5cGUiLCJTdHJpbmciLCJyZXF1aXJlZCIsInRyaW0iLCJtYXhsZW5ndGgiLCJwcm9kdWN0TmFtZSIsInByb2R1Y3RQcmljZSIsIk51bWJlciIsIm1pbiIsInF1YW50aXR5IiwidmFsaWRhdGUiLCJ2YWxpZGF0b3IiLCJ2YWx1ZSIsImlzSW50ZWdlciIsIm1lc3NhZ2UiLCJ0b3RhbEFtb3VudCIsImRhdGVPZkRlYnQiLCJEYXRlIiwiZGVmYXVsdCIsIm5vdyIsImlzUGFpZCIsIkJvb2xlYW4iLCJwYWlkRGF0ZSIsIm5vdGVzIiwidGltZXN0YW1wcyIsInRvSlNPTiIsInZpcnR1YWxzIiwidG9PYmplY3QiLCJpbmRleCIsInZpcnR1YWwiLCJnZXQiLCJkZWJ0RGF0ZSIsImRpZmZUaW1lIiwiTWF0aCIsImFicyIsImdldFRpbWUiLCJjZWlsIiwicHJlIiwibmV4dCIsInVuZGVmaW5lZCIsInN0YXRpY3MiLCJnZXREZWJ0U3VtbWFyeUJ5Q3VzdG9tZXIiLCJkZWJ0cyIsImZpbmQiLCJzb3J0IiwidG90YWxEZWJ0IiwicmVkdWNlIiwic3VtIiwiZGVidCIsInRvdGFsVW5wYWlkIiwiZmlsdGVyIiwiZGVidENvdW50IiwibGVuZ3RoIiwidW5wYWlkQ291bnQiLCJtb2RlbHMiLCJDdXN0b21lckRlYnQiLCJtb2RlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/CustomerDebt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    image: {\n        type: String,\n        trim: true,\n        default: ''\n    },\n    netWeight: {\n        type: String,\n        required: [\n            true,\n            'Net weight is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Net weight cannot exceed 50 characters'\n        ]\n    },\n    price: {\n        type: Number,\n        required: [\n            true,\n            'Price is required'\n        ],\n        min: [\n            0,\n            'Price cannot be negative'\n        ],\n        validate: {\n            validator: function(value) {\n                return value >= 0;\n            },\n            message: 'Price must be a positive number'\n        }\n    },\n    stockQuantity: {\n        type: Number,\n        required: [\n            true,\n            'Stock quantity is required'\n        ],\n        min: [\n            0,\n            'Stock quantity cannot be negative'\n        ],\n        default: 0\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: {\n            values: [\n                'snacks',\n                'canned goods',\n                'beverages',\n                'personal care',\n                'household',\n                'condiments',\n                'instant foods',\n                'dairy',\n                'frozen',\n                'others'\n            ],\n            message: 'Invalid category'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nProductSchema.index({\n    name: 1\n});\nProductSchema.index({\n    category: 1\n});\nProductSchema.index({\n    stockQuantity: 1\n});\n// Virtual for low stock indicator\nProductSchema.virtual('isLowStock').get(function() {\n    return this.stockQuantity <= 5;\n});\n// Pre-save middleware to ensure data consistency\nProductSchema.pre('save', function(next) {\n    if (this.price < 0) {\n        this.price = 0;\n    }\n    if (this.stockQuantity < 0) {\n        this.stockQuantity = 0;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Product', ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        // Check if connection is still alive\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1) {\n            return cached.conn;\n        } else {\n            // Reset cached connection if it's not alive\n            cached.conn = null;\n            cached.promise = null;\n        }\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log('✅ Connected to MongoDB');\n            return mongoose.connection;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', e);\n        throw new Error('Failed to connect to database');\n    }\n    return cached.conn;\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Handle process termination\nprocess.on('SIGINT', async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n    console.log('🛑 MongoDB connection closed due to app termination');\n    process.exit(0);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vbmdvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBU2hDLE1BQU1DLGNBQ0pDLFFBQVFDLEdBQUcsQ0FBQ0YsV0FBVyxJQUFJO0FBRTdCLElBQUksQ0FBQ0EsYUFBYTtJQUNoQixNQUFNLElBQUlHLE1BQ1I7QUFFSjtBQUVBOzs7O0NBSUMsR0FDRCxJQUFJQyxTQUFTQyxPQUFPTixRQUFRO0FBRTVCLElBQUksQ0FBQ0ssUUFBUTtJQUNYQSxTQUFTQyxPQUFPTixRQUFRLEdBQUc7UUFBRU8sTUFBTTtRQUFNQyxTQUFTO0lBQUs7QUFDekQ7QUFFQSxlQUFlQztJQUNiLElBQUlKLE9BQU9FLElBQUksRUFBRTtRQUNmLHFDQUFxQztRQUNyQyxJQUFJUCw0REFBbUIsQ0FBQ1csVUFBVSxLQUFLLEdBQUc7WUFDeEMsT0FBT04sT0FBT0UsSUFBSTtRQUNwQixPQUFPO1lBQ0wsNENBQTRDO1lBQzVDRixPQUFPRSxJQUFJLEdBQUc7WUFDZEYsT0FBT0csT0FBTyxHQUFHO1FBQ25CO0lBQ0Y7SUFFQSxJQUFJLENBQUNILE9BQU9HLE9BQU8sRUFBRTtRQUNuQixNQUFNSSxPQUFPO1lBQ1hDLGdCQUFnQjtZQUNoQkMsYUFBYTtZQUNiQywwQkFBMEI7WUFDMUJDLGlCQUFpQjtZQUNqQkMsUUFBUTtRQUNWO1FBRUFaLE9BQU9HLE9BQU8sR0FBR1IsdURBQWdCLENBQUNDLGFBQWFXLE1BQU1PLElBQUksQ0FBQyxDQUFDbkI7WUFDekRvQixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPckIsU0FBU1UsVUFBVTtRQUM1QjtJQUNGO0lBRUEsSUFBSTtRQUNGTCxPQUFPRSxJQUFJLEdBQUcsTUFBTUYsT0FBT0csT0FBTztJQUNwQyxFQUFFLE9BQU9jLEdBQUc7UUFDVmpCLE9BQU9HLE9BQU8sR0FBRztRQUNqQlksUUFBUUcsS0FBSyxDQUFDLCtCQUErQkQ7UUFDN0MsTUFBTSxJQUFJbEIsTUFBTTtJQUNsQjtJQUVBLE9BQU9DLE9BQU9FLElBQUk7QUFDcEI7QUFFQSw0QkFBNEI7QUFDNUJQLDBEQUFtQixDQUFDd0IsRUFBRSxDQUFDLGFBQWE7SUFDbENKLFFBQVFDLEdBQUcsQ0FBQztBQUNkO0FBRUFyQiwwREFBbUIsQ0FBQ3dCLEVBQUUsQ0FBQyxTQUFTLENBQUNDO0lBQy9CTCxRQUFRRyxLQUFLLENBQUMsZ0NBQWdDRTtBQUNoRDtBQUVBekIsMERBQW1CLENBQUN3QixFQUFFLENBQUMsZ0JBQWdCO0lBQ3JDSixRQUFRQyxHQUFHLENBQUM7QUFDZDtBQUVBLDZCQUE2QjtBQUM3Qm5CLFFBQVFzQixFQUFFLENBQUMsVUFBVTtJQUNuQixNQUFNeEIsMERBQW1CLENBQUMwQixLQUFLO0lBQy9CTixRQUFRQyxHQUFHLENBQUM7SUFDWm5CLFFBQVF5QixJQUFJLENBQUM7QUFDZjtBQUVBLGlFQUFlbEIsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxERUxMXFxPbmVEcml2ZVxcRGVza3RvcFxcdGluZGFoYW5cXGFwcFxcc3JjXFxsaWJcXG1vbmdvZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gJ21vbmdvb3NlJztcblxuZGVjbGFyZSBnbG9iYWwge1xuICB2YXIgbW9uZ29vc2U6IHtcbiAgICBjb25uOiBhbnkgfCBudWxsO1xuICAgIHByb21pc2U6IFByb21pc2U8YW55PiB8IG51bGw7XG4gIH07XG59XG5cbmNvbnN0IE1PTkdPREJfVVJJID1cbiAgcHJvY2Vzcy5lbnYuTU9OR09EQl9VUkkgfHwgJ21vbmdvZGI6Ly9sb2NhbGhvc3Q6MjcwMTcvc2FyaS1zYXJpLXN0b3JlJztcblxuaWYgKCFNT05HT0RCX1VSSSkge1xuICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgJ1BsZWFzZSBkZWZpbmUgdGhlIE1PTkdPREJfVVJJIGVudmlyb25tZW50IHZhcmlhYmxlIGluc2lkZSAuZW52LmxvY2FsJ1xuICApO1xufVxuXG4vKipcbiAqIEdsb2JhbCBpcyB1c2VkIGhlcmUgdG8gbWFpbnRhaW4gYSBjYWNoZWQgY29ubmVjdGlvbiBhY3Jvc3MgaG90IHJlbG9hZHNcbiAqIGluIGRldmVsb3BtZW50LiBUaGlzIHByZXZlbnRzIGNvbm5lY3Rpb25zIGdyb3dpbmcgZXhwb25lbnRpYWxseVxuICogZHVyaW5nIEFQSSBSb3V0ZSB1c2FnZS5cbiAqL1xubGV0IGNhY2hlZCA9IGdsb2JhbC5tb25nb29zZTtcblxuaWYgKCFjYWNoZWQpIHtcbiAgY2FjaGVkID0gZ2xvYmFsLm1vbmdvb3NlID0geyBjb25uOiBudWxsLCBwcm9taXNlOiBudWxsIH07XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGNvbm5lY3REQigpIHtcbiAgaWYgKGNhY2hlZC5jb25uKSB7XG4gICAgLy8gQ2hlY2sgaWYgY29ubmVjdGlvbiBpcyBzdGlsbCBhbGl2ZVxuICAgIGlmIChtb25nb29zZS5jb25uZWN0aW9uLnJlYWR5U3RhdGUgPT09IDEpIHtcbiAgICAgIHJldHVybiBjYWNoZWQuY29ubjtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gUmVzZXQgY2FjaGVkIGNvbm5lY3Rpb24gaWYgaXQncyBub3QgYWxpdmVcbiAgICAgIGNhY2hlZC5jb25uID0gbnVsbDtcbiAgICAgIGNhY2hlZC5wcm9taXNlID0gbnVsbDtcbiAgICB9XG4gIH1cblxuICBpZiAoIWNhY2hlZC5wcm9taXNlKSB7XG4gICAgY29uc3Qgb3B0cyA9IHtcbiAgICAgIGJ1ZmZlckNvbW1hbmRzOiBmYWxzZSxcbiAgICAgIG1heFBvb2xTaXplOiAxMCxcbiAgICAgIHNlcnZlclNlbGVjdGlvblRpbWVvdXRNUzogNTAwMCxcbiAgICAgIHNvY2tldFRpbWVvdXRNUzogNDUwMDAsXG4gICAgICBmYW1pbHk6IDRcbiAgICB9O1xuXG4gICAgY2FjaGVkLnByb21pc2UgPSBtb25nb29zZS5jb25uZWN0KE1PTkdPREJfVVJJLCBvcHRzKS50aGVuKChtb25nb29zZSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBDb25uZWN0ZWQgdG8gTW9uZ29EQicpO1xuICAgICAgcmV0dXJuIG1vbmdvb3NlLmNvbm5lY3Rpb247XG4gICAgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGNhY2hlZC5jb25uID0gYXdhaXQgY2FjaGVkLnByb21pc2U7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjYWNoZWQucHJvbWlzZSA9IG51bGw7XG4gICAgY29uc29sZS5lcnJvcign4p2MIE1vbmdvREIgY29ubmVjdGlvbiBlcnJvcjonLCBlKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjb25uZWN0IHRvIGRhdGFiYXNlJyk7XG4gIH1cblxuICByZXR1cm4gY2FjaGVkLmNvbm47XG59XG5cbi8vIENvbm5lY3Rpb24gZXZlbnQgaGFuZGxlcnNcbm1vbmdvb3NlLmNvbm5lY3Rpb24ub24oJ2Nvbm5lY3RlZCcsICgpID0+IHtcbiAgY29uc29sZS5sb2coJ/CflJcgTW9uZ29vc2UgY29ubmVjdGVkIHRvIE1vbmdvREInKTtcbn0pO1xuXG5tb25nb29zZS5jb25uZWN0aW9uLm9uKCdlcnJvcicsIChlcnIpID0+IHtcbiAgY29uc29sZS5lcnJvcign4p2MIE1vbmdvb3NlIGNvbm5lY3Rpb24gZXJyb3I6JywgZXJyKTtcbn0pO1xuXG5tb25nb29zZS5jb25uZWN0aW9uLm9uKCdkaXNjb25uZWN0ZWQnLCAoKSA9PiB7XG4gIGNvbnNvbGUubG9nKCfwn5SMIE1vbmdvb3NlIGRpc2Nvbm5lY3RlZCBmcm9tIE1vbmdvREInKTtcbn0pO1xuXG4vLyBIYW5kbGUgcHJvY2VzcyB0ZXJtaW5hdGlvblxucHJvY2Vzcy5vbignU0lHSU5UJywgYXN5bmMgKCkgPT4ge1xuICBhd2FpdCBtb25nb29zZS5jb25uZWN0aW9uLmNsb3NlKCk7XG4gIGNvbnNvbGUubG9nKCfwn5uRIE1vbmdvREIgY29ubmVjdGlvbiBjbG9zZWQgZHVlIHRvIGFwcCB0ZXJtaW5hdGlvbicpO1xuICBwcm9jZXNzLmV4aXQoMCk7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgY29ubmVjdERCO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiTU9OR09EQl9VUkkiLCJwcm9jZXNzIiwiZW52IiwiRXJyb3IiLCJjYWNoZWQiLCJnbG9iYWwiLCJjb25uIiwicHJvbWlzZSIsImNvbm5lY3REQiIsImNvbm5lY3Rpb24iLCJyZWFkeVN0YXRlIiwib3B0cyIsImJ1ZmZlckNvbW1hbmRzIiwibWF4UG9vbFNpemUiLCJzZXJ2ZXJTZWxlY3Rpb25UaW1lb3V0TVMiLCJzb2NrZXRUaW1lb3V0TVMiLCJmYW1pbHkiLCJjb25uZWN0IiwidGhlbiIsImNvbnNvbGUiLCJsb2ciLCJlIiwiZXJyb3IiLCJvbiIsImVyciIsImNsb3NlIiwiZXhpdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();