/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/debts/route";
exports.ids = ["app/api/debts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebts%2Froute&page=%2Fapi%2Fdebts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebts%2Froute&page=%2Fapi%2Fdebts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_debts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/debts/route.ts */ \"(rsc)/./src/app/api/debts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/debts/route\",\n        pathname: \"/api/debts\",\n        filename: \"route\",\n        bundlePath: \"app/api/debts/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\debts\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_debts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebts%2Froute&page=%2Fapi%2Fdebts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/debts/route.ts":
/*!************************************!*\
  !*** ./src/app/api/debts/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/CustomerDebt */ \"(rsc)/./src/lib/models/CustomerDebt.ts\");\n\n\n\n// GET /api/debts - Get all debts\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const customer = searchParams.get('customer');\n        const isPaid = searchParams.get('isPaid');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const skip = (page - 1) * limit;\n        // Build query\n        const query = {};\n        if (customer) {\n            query.customerName = {\n                $regex: customer,\n                $options: 'i'\n            };\n        }\n        if (isPaid !== null && isPaid !== undefined) {\n            query.isPaid = isPaid === 'true';\n        }\n        // Get debts with pagination\n        const debts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(query).sort({\n            dateOfDebt: -1\n        }).skip(skip).limit(limit);\n        // Get total count for pagination\n        const total = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: debts,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching debts:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch debts'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/debts - Create a new debt\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const body = await request.json();\n        const { customerName, productName, productPrice, quantity, dateOfDebt, notes } = body;\n        // Validation\n        if (!customerName || !productName || !productPrice || !quantity) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        if (productPrice <= 0 || quantity <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Price and quantity must be positive'\n            }, {\n                status: 400\n            });\n        }\n        const debt = new _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            customerName: customerName.trim(),\n            productName: productName.trim(),\n            productPrice: parseFloat(productPrice),\n            quantity: parseInt(quantity),\n            dateOfDebt: dateOfDebt ? new Date(dateOfDebt) : new Date(),\n            notes: notes || '',\n            isPaid: false\n        });\n        await debt.save();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: debt,\n            message: 'Debt created successfully'\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating debt:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create debt'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/debts/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/CustomerDebt.ts":
/*!****************************************!*\
  !*** ./src/lib/models/CustomerDebt.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomerDebtSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    customerName: {\n        type: String,\n        required: [\n            true,\n            'Customer name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Customer name cannot exceed 100 characters'\n        ]\n    },\n    productName: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    productPrice: {\n        type: Number,\n        required: [\n            true,\n            'Product price is required'\n        ],\n        min: [\n            0,\n            'Product price cannot be negative'\n        ]\n    },\n    quantity: {\n        type: Number,\n        required: [\n            true,\n            'Quantity is required'\n        ],\n        min: [\n            1,\n            'Quantity must be at least 1'\n        ],\n        validate: {\n            validator: function(value) {\n                return Number.isInteger(value) && value > 0;\n            },\n            message: 'Quantity must be a positive integer'\n        }\n    },\n    totalAmount: {\n        type: Number,\n        required: [\n            true,\n            'Total amount is required'\n        ],\n        min: [\n            0,\n            'Total amount cannot be negative'\n        ]\n    },\n    dateOfDebt: {\n        type: Date,\n        required: [\n            true,\n            'Date of debt is required'\n        ],\n        default: Date.now\n    },\n    isPaid: {\n        type: Boolean,\n        default: false\n    },\n    paidDate: {\n        type: Date,\n        default: null\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Notes cannot exceed 500 characters'\n        ],\n        default: ''\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nCustomerDebtSchema.index({\n    customerName: 1\n});\nCustomerDebtSchema.index({\n    isPaid: 1\n});\nCustomerDebtSchema.index({\n    dateOfDebt: -1\n});\nCustomerDebtSchema.index({\n    customerName: 1,\n    isPaid: 1\n});\n// Virtual for days since debt was created\nCustomerDebtSchema.virtual('daysSinceDebt').get(function() {\n    const now = new Date();\n    const debtDate = new Date(this.dateOfDebt);\n    const diffTime = Math.abs(now.getTime() - debtDate.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware to calculate total amount and handle paid date\nCustomerDebtSchema.pre('save', function(next) {\n    // Calculate total amount\n    this.totalAmount = this.productPrice * this.quantity;\n    // Set paid date when marking as paid\n    if (this.isPaid && !this.paidDate) {\n        this.paidDate = new Date();\n    }\n    // Clear paid date when marking as unpaid\n    if (!this.isPaid && this.paidDate) {\n        this.paidDate = undefined;\n    }\n    next();\n});\n// Static method to get debt summary by customer\nCustomerDebtSchema.statics.getDebtSummaryByCustomer = async function(customerName) {\n    const debts = await this.find({\n        customerName\n    }).sort({\n        dateOfDebt: -1\n    });\n    const totalDebt = debts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    const totalUnpaid = debts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    return {\n        customerName,\n        totalDebt,\n        totalUnpaid,\n        debtCount: debts.length,\n        unpaidCount: debts.filter((debt)=>!debt.isPaid).length,\n        debts\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CustomerDebt || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('CustomerDebt', CustomerDebtSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/CustomerDebt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        // Check if connection is still alive\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1) {\n            return cached.conn;\n        } else {\n            // Reset cached connection if it's not alive\n            cached.conn = null;\n            cached.promise = null;\n        }\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log('✅ Connected to MongoDB');\n            return mongoose.connection;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', e);\n        throw new Error('Failed to connect to database');\n    }\n    return cached.conn;\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Handle process termination\nprocess.on('SIGINT', async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n    console.log('🛑 MongoDB connection closed due to app termination');\n    process.exit(0);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdebts%2Froute&page=%2Fapi%2Fdebts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();